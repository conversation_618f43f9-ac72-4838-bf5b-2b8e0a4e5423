import { ForgeRockBridge } from "@roshn/shared/forgerock-bridge";
import { Container } from "inversify";

import { AuthService } from "~/interface/authTypes";
import { FRAuthServiceImpl } from "~/services/auth/implementations/forge-rock/fr-auth-impl";
import { ShopboxoAuthImpl } from "~/services/auth/implementations/shopboxo/shopboxo-auth-impl";
import { ForgeRockBridgeWeb } from "~/services/bridge/base-forgerock-bridge";
import { HttpClientFactory, HttpClientFactoryImpl } from "~/services/http-client-factory";
import { ProductService } from "~/services/product-list/product-list";
import { ProductListImpl } from "~/services/product-list/product-list-impl";
import { ReactQueryClient, ReactQueryClientImpl } from "~/services/react-query-client";
import { RestHelper, RestHelperImpl } from "~/services/rest-helper";
import { StoreFactory, StoreFactoryImpl } from "~/services/store-factory";
import { ThemeService } from "~/services/theme-service/theme-service";
import { ThemeServiceImpl } from "~/services/theme-service/theme-service-impl";
import { TranslationService, TranslationServiceImpl } from "~/services/translation";

import { StrapiImpl, StrapiService } from "../services/strapi";
import { ProjectService } from "~/interface/projectTypes";
import { ProjectServiceImpl } from "~/services/project/project-impl";

export function bindImplsCommon(container: Container) {
  // Bind services
  container.bind<HttpClientFactory>(HttpClientFactory).to(HttpClientFactoryImpl);
  container.bind<StrapiService>(StrapiService).to(StrapiImpl);
  container.bind<TranslationService>(TranslationService).to(TranslationServiceImpl);
  container.bind<RestHelper>(RestHelper).to(RestHelperImpl);
  container.bind<ReactQueryClient>(ReactQueryClient).to(ReactQueryClientImpl);
  container.bind<ProductService>(ProductService).to(ProductListImpl);
  container.bind<ForgeRockBridge>(ForgeRockBridge).to(ForgeRockBridgeWeb);
  container.bind<StoreFactory>(StoreFactory).to(StoreFactoryImpl);
  container.bind<ThemeService>(ThemeService).to(ThemeServiceImpl);
  container.bind<ProjectService>(ProjectService).to(ProjectServiceImpl);

  if (import.meta.env.VITE_AUTH_TYPE === "SHOP_BOXO") {
    container.bind<AuthService>(AuthService).to(ShopboxoAuthImpl);
  } else {
    container.bind<AuthService>(AuthService).to(FRAuthServiceImpl);
  }
}
