import { css, useTheme } from "@emotion/react";
import { useNavigate } from "@remix-run/react";
import {
  RDSTable,
  RDSTagInteractive,
  RDSButton,
  RDSTypography,
  AppTheme,
  RDSSearchInput,
  RDSEmptyState,
  RDSPagination,
  Image,
} from "@roshn/ui-kit";
import { RoshnContainerLoader } from "~/features/common/loading/roshn-container-loader";

import { useAppPath } from "~/hooks/use-app-path";
import { useProjectList } from "~/services/project/hooks/use-project";
import { AppPaths } from "~/utils/app-paths";

const tagData = [
  { label: "All", state: "active" },
  { label: "Drafted", state: "default" },
  { label: "In review", state: "default" },
  { label: "Published", state: "default" },
];

const statusVariantMap = [
  { status: "IN REVIEW", variant: "warning" },
  { status: "REJECTED", variant: "danger" },
  { status: "DRAFT", variant: "info" },
  { status: "APPROVED", variant: "success" },
];

const visibilityVariantMap = [
  { visibility: "VISIBLE", variant: "success" },
  { visibility: "HIDDEN", variant: "neutral" },
];

type StatusVariantMapType = {
  status: string;
  variant: string;
};

const tableData = {
  columns: [
    {
      id: "column1",
      header: "Projects",
      accessor: "column1",
      type: "lead",
    },
    {
      id: "column2",
      header: "Units Sold",
      accessor: "column2",
      type: "text",
    },
    {
      id: "column3",
      header: "Listing Status",
      accessor: "column3",
      type: "tag",
    },
    {
      id: "column4",
      header: "Visibility",
      accessor: "column4",
      type: "tag",
    },
    {
      id: "actions",
      header: "Actions",
      accessor: "id",
      type: "action",
    },
  ],
};

const styles = {
  wrapper: (theme: AppTheme) =>
    css({
      display: "flex",
      flexDirection: "column",
      padding: theme.rds.dimension["600"],
      background: theme?.rds?.color?.background?.brand?.secondary?.inverse?.default,
      minHeight: "100vh",
      gap: theme.rds.dimension["200"],
    }),
  headerContainer: (theme: AppTheme) =>
    css({
      display: "flex",
      flexDirection: "column",
      gap: theme.rds.dimension["400"],
    }),

  header: () =>
    css({
      display: "flex",
      justifyContent: "space-between",
      alignItems: "center",
      alignContent: "center",
    }),
  searchInput: (theme: AppTheme) =>
    css({
      gap: theme.rds.dimension["200"],
    }),
  tagContainer: (theme: AppTheme) =>
    css({
      display: "flex",
      gap: theme.rds.dimension["200"],
      marginTop: theme.rds.dimension["300"],
    }),
};

export default function ProjectsPage() {
  const theme = useTheme() as AppTheme;
  const generatePath = useAppPath();
  const navigate = useNavigate();
  const { data, isFetching } = useProjectList();
  const results = data?.results || [];

  const handleAddProject = () => {
    navigate(generatePath(AppPaths.addProject));
  };

  const handleProjectDetail = () => {
    navigate(generatePath(AppPaths.projectDetail));
  };

  const tagTypeVariant = (variant:StatusVariantMapType[], status: string) => {
    const found = variant.find((item) => item.status === status.toUpperCase());
    return found ? found.variant : "info";
  };

  const leadIconProject = (src: string) => (
    <Image height="28px" width="88px" srcSet={[src]} alt="Roshn Logo" />
  );

  const columnData = [...[], ...results].map((projectData: unknown, index: number) => ({
    id: `row-${index}`,
    column1: {
      dataValue: (
        <div css={{ cursor: "pointer" }} onClick={handleProjectDetail}>
          {projectData?.title}
        </div>
      ),
      leadIcon: leadIconProject(projectData?.projectIcon),
    },
    column2: { dataValue: projectData?.unitsSold },
    column3: {
      dataValue: projectData?.status,
      tagType: tagTypeVariant(statusVariantMap,projectData?.status),
    },
    column4: {
      dataValue: projectData?.visibility,
      tagType: tagTypeVariant(visibilityVariantMap, projectData?.visibility),
    },
  }));

  return (
    <div css={styles.wrapper(theme)}>
      <div css={styles.headerContainer(theme)}>
        <div css={styles.header(theme)}>
          <div>
            <RDSTypography fontName={theme?.rds?.typographies?.display?.d5}>Projects</RDSTypography>
          </div>
          <div>
            <RDSButton onClick={handleAddProject} size="lg" text="+ Add project" />
          </div>
        </div>
        <div>
          <RDSSearchInput
            type="text"
            placeholder="Search by name, city, status..."
            css={styles.searchInput(theme)}
          />
          <div css={styles.tagContainer(theme)}>
            {tagData.map((tag) => (
              <RDSTagInteractive
                key={tag.label}
                label={tag.label}
                state={tag.state as "default" | "active"}
              />
            ))}
          </div>
        </div>
      </div>
      {isFetching ? (
        <RoshnContainerLoader />
      ) : true ? (
        <RDSTable
          actionText={"View Project"}
          title={tableData.title}
          description={tableData.description}
          columns={tableData.columns}
          data={columnData}
          slotBottom={<RDSPagination pageCount={10} activePage={1} onPageChange={() => {}} />}
        />
      ) : (
        <RDSEmptyState
          showMedia={false}
          title="You don’t have any listed projects"
          size="sm"
          description="Projects you create will appear here. Add new projects now to fill up your list."
          showFooter={true}
          buttons={[
            {
              text: "ADD YOUR FIRST PROJECT",
              variant: "primary",
              onClick: handleAddProject,
            },
          ]}
        />
      )}
    </div>
  );
}
