import { useQuery } from "@tanstack/react-query";

import { ProjectService } from "~/interface/projectTypes";

import { useInjection } from "../../../hooks/use-di";

export function useProjectList() {
  const projectService = useInjection<ProjectService>(ProjectService);

  return useQuery({
    queryKey: ['projectList'],
    queryFn: () => projectService.getProjectList(),
    refetchOnReconnect: false,
    refetchOnWindowFocus: false,
  });
}