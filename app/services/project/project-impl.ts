import { inject, injectable } from "inversify";
import { ProjectService } from "~/interface/projectTypes";

import { ReactQueryClient } from "~/services/react-query-client";
import { RestHelper } from "~/services/rest-helper";

const mockProjectListData = {
  results: [
    {
      projectIcon: "https://************.nip.io/roshn_group_logo_with_text_4b8be975b2/roshn_group_logo_with_text_4b8be975b2.svg",
      title: "Project 2",
      status: "Approved",
      unitsSold: "25/100",
      visibility: "Visible",
    },
    {
      projectIcon: "https://************.nip.io/roshn_group_logo_with_text_4b8be975b2/roshn_group_logo_with_text_4b8be975b2.svg",
      title: "Project 3",
      status: "Rejected",
      unitsSold: "36/100",
      visibility: "Hidden",
    },
    {
      projectIcon: "https://************.nip.io/roshn_group_logo_with_text_4b8be975b2/roshn_group_logo_with_text_4b8be975b2.svg",
      title: "Project 4",
      status: "Draft",
      unitsSold: "1/100",
      visibility: "Hidden",
    },
    {
      projectIcon: "https://************.nip.io/roshn_group_logo_with_text_4b8be975b2/roshn_group_logo_with_text_4b8be975b2.svg",
      title: "Project 1",
      status: "In Review",
      unitsSold: "10/100",
      visibility: "Hidden",
    },
  ],
};


@injectable()
export class ProjectServiceImpl implements ProjectService {
  constructor(
    @inject(RestHelper)
    private readonly restHelper: RestHelper,
    @inject(ReactQueryClient)
    private readonly reactQueryClient: ReactQueryClient,
  ) {}

  getProjectList: () => Promise<any> = () => Promise.resolve(mockProjectListData);
}
